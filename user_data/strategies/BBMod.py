import warnings
from datetime import datetime
from functools import reduce

import freqtrade.vendor.qtpylib.indicators as qtpylib
import numpy as np
import pandas as pd
import pandas_ta as pta
import talib.abstract as ta
from freqtrade.persistence import Trade
from freqtrade.strategy import (
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    informative,
    stoploss_from_open,
)
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame, Series

warnings.simplefilter(action="ignore", category=pd.errors.PerformanceWarning)
# 忽略pandas_ta中pkg_resources弃用警告
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")


# 自定义技术指标函数
# ##################################################################################################


def ha_typical_price(bars):
    """
    计算Heiken Ashi典型价格

    Args:
        bars: 包含ha_high, ha_low, ha_close的DataFrame

    Returns:
        Series: <PERSON>iken Ashi典型价格 = (最高价 + 最低价 + 收盘价) / 3
    """
    res = (bars["ha_high"] + bars["ha_low"] + bars["ha_close"]) / 3.0
    return Series(index=bars.index, data=res)


def ewo(dataframe, ema_length=5, ema2_length=35):
    """
    Elliott Wave Oscillator (EWO) - 艾略特波浪震荡器

    计算两个不同周期EMA的差值，用于识别趋势强度和方向

    Args:
        dataframe: OHLCV数据
        ema_length: 短期EMA周期，默认5
        ema2_length: 长期EMA周期，默认35

    Returns:
        Series: EWO值，正值表示上升趋势，负值表示下降趋势
    """
    df = dataframe.copy()
    ema1 = ta.EMA(df, timeperiod=ema_length)
    ema2 = ta.EMA(df, timeperiod=ema2_length)
    emadif = (ema1 - ema2) / df["low"] * 100
    return emadif


def vwap_b(dataframe, window_size=20, num_of_std=1):
    """
    VWAP布林带 - 基于成交量加权平均价格的布林带

    计算VWAP及其上下轨，用于识别价格相对于成交量加权平均价格的位置

    Args:
        dataframe: OHLCV数据
        window_size: 计算窗口大小，默认20
        num_of_std: 标准差倍数，默认1

    Returns:
        tuple: (vwap_low, vwap, vwap_high) - VWAP下轨、中轨、上轨
    """
    df = dataframe.copy()
    df["vwap"] = qtpylib.rolling_vwap(df, window=window_size)
    rolling_std = df["vwap"].rolling(window=window_size).std()
    df["vwap_low"] = df["vwap"] - (rolling_std * num_of_std)
    df["vwap_high"] = df["vwap"] + (rolling_std * num_of_std)
    return df["vwap_low"], df["vwap"], df["vwap_high"]


def top_percent_change(dataframe: DataFrame, length: int) -> float:
    """
    顶部百分比变化 - 计算当前收盘价相对于历史最高开盘价的变化百分比

    用于衡量价格从历史高点的回撤程度，帮助识别潜在的买入机会

    Args:
        dataframe: OHLC数据框
        length: 回看周期长度，0表示只看当前K线

    Returns:
        float: 百分比变化值，负值表示价格低于历史高点
    """
    if length == 0:
        return (dataframe["open"] - dataframe["close"]) / dataframe["close"]
    else:
        return (dataframe["open"].rolling(length).max() - dataframe["close"]) / dataframe["close"]


# Williams %R - 威廉指标
def williams_r(dataframe: DataFrame, period: int = 14) -> Series:
    """
    威廉指标 (%R) - 动量震荡指标

    衡量收盘价在指定周期内最高价和最低价区间中的相对位置
    数值范围：-100到0，-20以上为超买，-80以下为超卖

    Args:
        dataframe: OHLC数据框
        period: 计算周期，默认14

    Returns:
        Series: Williams %R值，负值表示相对位置
    """
    highest_high = dataframe["high"].rolling(center=False, window=period).max()
    lowest_low = dataframe["low"].rolling(center=False, window=period).min()

    wr = Series(
        (highest_high - dataframe["close"]) / (highest_high - lowest_low),
        name=f"{period} Williams %R",
    )

    return wr * -100


def T3(dataframe, length=5):
    """
    T3平均线 - 三重指数平滑移动平均线

    T3是一种更平滑的移动平均线，减少了滞后性，对价格变化更敏感
    由HPotter在Tradingview上开发

    Args:
        dataframe: OHLC数据框
        length: 平滑周期，默认5

    Returns:
        Series: T3平均线值

    参考: https://www.tradingview.com/script/qzoC9H1I-T3-Average/
    """
    df = dataframe.copy()

    df["xe1"] = ta.EMA(df["close"], timeperiod=length)
    df["xe2"] = ta.EMA(df["xe1"], timeperiod=length)
    df["xe3"] = ta.EMA(df["xe2"], timeperiod=length)
    df["xe4"] = ta.EMA(df["xe3"], timeperiod=length)
    df["xe5"] = ta.EMA(df["xe4"], timeperiod=length)
    df["xe6"] = ta.EMA(df["xe5"], timeperiod=length)
    b = 0.7
    c1 = -b * b * b
    c2 = 3 * b * b + 3 * b * b * b
    c3 = -6 * b * b - 3 * b - 3 * b * b * b
    c4 = 1 + 3 * b + b * b * b + 3 * b * b
    df["T3Average"] = c1 * df["xe6"] + c2 * df["xe5"] + c3 * df["xe4"] + c4 * df["xe3"]

    return df["T3Average"]


def rmi(dataframe, *, length=20, mom=5):
    """
    相对动量指标 (RMI) - Relative Momentum Index

    RMI是RSI的改进版本，使用动量而不是简单的价格变化来计算
    能够更好地识别超买超卖状态，减少假信号

    Args:
        dataframe: OHLC数据框
        length: EMA平滑周期，默认20
        mom: 动量周期（价格比较的间隔），默认5

    Returns:
        Series: RMI值，范围0-100，70以上超买，30以下超卖
    """
    df = dataframe.copy()
    df["maxup"] = (df["close"] - df["close"].shift(mom)).clip(lower=0)
    df["maxdown"] = (df["close"].shift(mom) - df["close"]).clip(lower=0)

    # 只对数值列进行fillna操作，避免datetime列的类型冲突
    df["maxup"] = df["maxup"].fillna(0)
    df["maxdown"] = df["maxdown"].fillna(0)

    df["emaInc"] = ta.EMA(df, price="maxup", timeperiod=length)
    df["emaDec"] = ta.EMA(df, price="maxdown", timeperiod=length)

    df["RMI"] = np.where(df["emaDec"] == 0, 0, 100 - 100 / (1 + df["emaInc"] / df["emaDec"]))
    return df["RMI"]


# #####################################################################################################


class BBMod(IStrategy):
    """
    BBMod策略 - 多条件布林带修改版双向交易策略

    这是一个复合型双向交易策略，结合了多种技术指标和条件：
    - 布林带突破检测
    - 挤压动量 (Squeeze Momentum)
    - Elliott Wave Oscillator (EWO)
    - 相对强弱指标 (RSI)
    - 威廉指标 (%R)
    - VWAP分析
    - Heiken Ashi蜡烛图分析

    策略特点：
    - 支持做多和做空双向交易
    - 多条件入场，提高信号质量
    - 自定义止损机制
    - 支持超参数优化
    - 5分钟时间框架，适合短线交易
    - 每种买入策略都有对应的做空策略
    """

    # 最小ROI设置为100%，实际通过自定义止损控制
    minimal_roi = {"0": 100}

    # 策略最佳时间框架
    timeframe = "5m"
    can_short = True  # 启用做空交易

    # 仅在新K线时运行指标计算，提高效率
    process_only_new_candles = True
    # 启动时需要的历史K线数量
    startup_candle_count = 200

    # 订单类型配置 - 全部使用市价单确保快速成交
    order_types = {
        "entry": "market",  # 入场订单：市价单
        "exit": "market",  # 出场订单：市价单
        "emergency_exit": "market",  # 紧急出场：市价单
        "force_entry": "market",  # 强制入场：市价单
        "force_exit": "market",  # 强制出场：市价单
        "stoploss": "market",  # 止损订单：市价单
        "stoploss_on_exchange": False,  # 不在交易所设置止损
        "stoploss_on_exchange_interval": 60,  # 交易所止损检查间隔
        "stoploss_on_exchange_market_ratio": 0.99,  # 交易所止损市价比例
    }

    # 固定止损设置为-99%（实际不使用，通过自定义止损控制）
    stoploss = -0.99

    # 启用自定义止损功能
    use_custom_stoploss = True

    # =============================================================================
    # 买入条件开关参数 - 控制各种买入策略的启用/禁用
    # =============================================================================
    buy_con_op = True  # 主开关，控制是否优化条件参数

    # 杠杆设置
    buy_leverage = DecimalParameter(1.0, 15.0, default=5.0, space="buy", optimize=True)  # 杠杆倍数（1-15倍）

    # 布林带检查策略开关
    buy_is_bb_checked_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # 挤压动量策略开关
    buy_is_sqzmom_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # EWO策略2开关
    buy_is_ewo_2_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # R死鱼策略开关
    buy_is_r_deadfish_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # ClucHA策略开关
    buy_is_clucHA_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # Cofi策略开关
    buy_is_cofi_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # Gumbo策略开关
    buy_is_gumbo_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # 本地上升趋势策略开关
    buy_is_local_uptrend_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # 本地上升趋势2策略开关
    buy_is_local_uptrend2_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # 本地下跌策略开关
    buy_is_local_dip_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # EWO策略开关
    buy_is_ewo_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # NFI32策略开关
    buy_is_nfi_32_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)

    # =============================================================================
    # 做空条件开关参数 - 控制各种做空策略的启用/禁用
    # =============================================================================
    sell_con_op = True  # 做空主开关，控制是否优化做空条件参数

    # 布林带检查做空策略开关
    sell_is_bb_checked_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # 挤压动量做空策略开关
    sell_is_sqzmom_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # EWO做空策略2开关
    sell_is_ewo_2_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # R死鱼做空策略开关
    sell_is_r_deadfish_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # ClucHA做空策略开关
    sell_is_clucHA_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # Cofi做空策略开关
    sell_is_cofi_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # Gumbo做空策略开关
    sell_is_gumbo_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # 本地下降趋势做空策略开关
    sell_is_local_downtrend_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # 本地下降趋势2做空策略开关
    sell_is_local_downtrend2_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # 本地上涨做空策略开关
    sell_is_local_pump_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # EWO做空策略开关
    sell_is_ewo_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # NFI32做空策略开关
    sell_is_nfi_32_enable = CategoricalParameter([True, False], default=True, space="sell", optimize=sell_con_op)
    # NFIX39策略开关
    buy_is_nfix_39_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)
    # VWAP策略开关
    buy_is_vwap_enable = CategoricalParameter([True, False], default=True, space="buy", optimize=buy_con_op)

    # =============================================================================
    # 下跌买入参数 - 用于识别超卖状态的买入机会
    # =============================================================================
    is_optimize_dip = True  # 下跌参数优化开关

    # RMI相对动量指标阈值（30以下超卖）
    buy_rmi = IntParameter(30, 50, default=35, space="buy", optimize=is_optimize_dip)
    # CCI商品通道指标阈值（-100以下超卖）
    buy_cci = IntParameter(-135, -90, default=-133, space="buy", optimize=is_optimize_dip)
    # 随机RSI快线阈值（20以下超卖）
    buy_srsi_fk = IntParameter(30, 50, default=25, space="buy", optimize=is_optimize_dip)
    # CCI计算周期长度
    buy_cci_length = IntParameter(25, 45, default=25, space="buy", optimize=is_optimize_dip)
    # RMI计算周期长度
    buy_rmi_length = IntParameter(8, 20, default=8, space="buy", optimize=is_optimize_dip)

    # =============================================================================
    # 突破买入参数 - 用于布林带突破策略
    # =============================================================================
    is_optimize_break = True  # 突破参数优化开关

    # 布林带宽度阈值（衡量波动性）
    buy_bb_width = DecimalParameter(0.065, 0.135, default=0.095, space="buy", optimize=is_optimize_break)
    # 布林带差值阈值（上下轨差异）
    buy_bb_delta = DecimalParameter(0.018, 0.035, default=0.025, space="buy", optimize=is_optimize_break)
    # 收盘价变化阈值（价格波动幅度）
    break_closedelta = DecimalParameter(12.0, 18.0, default=15.0, space="buy", optimize=is_optimize_break)
    # 布林带下轨买入因子（价格相对下轨的位置）
    break_buy_bb_factor = DecimalParameter(0.990, 0.999, default=0.995, space="buy", optimize=is_optimize_break)

    is_optimize_local_uptrend = True
    buy_ema_diff = DecimalParameter(0.022, 0.027, default=0.025, space="buy", optimize=is_optimize_local_uptrend)
    buy_bb_factor = DecimalParameter(0.990, 0.999, default=0.995, space="buy", optimize=is_optimize_local_uptrend)
    buy_closedelta = DecimalParameter(12.0, 18.0, default=15.0, space="buy", optimize=is_optimize_local_uptrend)

    is_optimize_local_dip = True
    buy_ema_diff_local_dip = DecimalParameter(0.022, 0.027, default=0.025, space="buy", optimize=is_optimize_local_dip)
    buy_ema_high_local_dip = DecimalParameter(0.90, 1.2, default=0.942, space="buy", optimize=is_optimize_local_dip)
    buy_closedelta_local_dip = DecimalParameter(12.0, 18.0, default=15.0, space="buy", optimize=is_optimize_local_dip)
    buy_rsi_local_dip = IntParameter(15, 45, default=28, space="buy", optimize=is_optimize_local_dip)
    buy_crsi_local_dip = IntParameter(10, 18, default=10, space="buy", optimize=is_optimize_local_dip)

    is_optimize_ewo = True
    buy_rsi_fast = IntParameter(35, 50, default=45, space="buy", optimize=is_optimize_ewo)
    buy_rsi = IntParameter(15, 35, default=35, space="buy", optimize=is_optimize_ewo)
    buy_ewo = DecimalParameter(-6.0, 5, default=-5.585, space="buy", optimize=is_optimize_ewo)
    buy_ema_low = DecimalParameter(0.9, 0.99, default=0.942, space="buy", optimize=is_optimize_ewo)
    buy_ema_high = DecimalParameter(0.95, 1.2, default=1.084, space="buy", optimize=is_optimize_ewo)

    is_optimize_nfix_39 = True
    buy_nfix_39_ema = DecimalParameter(0.9, 1.2, default=0.97, space="buy", optimize=is_optimize_nfix_39)

    is_optimize_sqzmom_protection = True
    buy_sqzmom_ema = DecimalParameter(0.9, 1.2, default=0.97, space="buy", optimize=is_optimize_sqzmom_protection)
    buy_sqzmom_ewo = DecimalParameter(-12, 12, default=0, space="buy", optimize=is_optimize_sqzmom_protection)
    buy_sqzmom_r14 = DecimalParameter(-100, -22, default=-50, space="buy", optimize=is_optimize_sqzmom_protection)

    is_optimize_ewo_2 = True
    buy_rsi_fast_ewo_2 = IntParameter(15, 50, default=45, space="buy", optimize=is_optimize_ewo_2)
    buy_rsi_ewo_2 = IntParameter(15, 50, default=35, space="buy", optimize=is_optimize_ewo_2)
    buy_ema_low_2 = DecimalParameter(0.90, 1.2, default=0.970, space="buy", optimize=is_optimize_ewo_2)
    buy_ema_high_2 = DecimalParameter(0.90, 1.2, default=1.087, space="buy", optimize=is_optimize_ewo_2)
    buy_ewo_high_2 = DecimalParameter(2, 12, default=4.179, space="buy", optimize=is_optimize_ewo_2)

    is_optimize_r_deadfish = True
    buy_r_deadfish_ema = DecimalParameter(0.90, 1.2, default=1.087, space="buy", optimize=is_optimize_r_deadfish)
    buy_r_deadfish_bb_width = DecimalParameter(0.03, 0.75, default=0.05, space="buy", optimize=is_optimize_r_deadfish)
    buy_r_deadfish_bb_factor = DecimalParameter(0.90, 1.2, default=1.0, space="buy", optimize=is_optimize_r_deadfish)
    buy_r_deadfish_volume_factor = DecimalParameter(1, 2.5, default=1.0, space="buy", optimize=is_optimize_r_deadfish)
    buy_r_deadfish_cti = DecimalParameter(-0.6, -0.0, default=-0.5, space="buy", optimize=is_optimize_r_deadfish)
    buy_r_deadfish_r14 = DecimalParameter(-60, -44, default=-60, space="buy", optimize=is_optimize_r_deadfish)

    is_optimize_cofi = True
    buy_roc_1h = IntParameter(-25, 200, default=10, space="buy", optimize=is_optimize_cofi)
    buy_bb_width_1h = DecimalParameter(0.3, 2.0, default=0.3, space="buy", optimize=is_optimize_cofi)
    buy_ema_cofi = DecimalParameter(0.94, 1.2, default=0.97, space="buy", optimize=is_optimize_cofi)
    buy_fastk = IntParameter(0, 40, default=20, space="buy", optimize=is_optimize_cofi)
    buy_fastd = IntParameter(0, 40, default=20, space="buy", optimize=is_optimize_cofi)
    buy_adx = IntParameter(0, 30, default=30, space="buy", optimize=is_optimize_cofi)
    buy_ewo_high = DecimalParameter(2, 12, default=3.553, space="buy", optimize=is_optimize_cofi)
    buy_cofi_cti = DecimalParameter(-0.9, -0.0, default=-0.5, space="buy", optimize=is_optimize_cofi)
    buy_cofi_r14 = DecimalParameter(-100, -44, default=-60, space="buy", optimize=is_optimize_cofi)

    is_optimize_clucha = True
    buy_clucha_bbdelta_close = DecimalParameter(0.01, 0.05, default=0.02206, space="buy", optimize=is_optimize_clucha)
    buy_clucha_bbdelta_tail = DecimalParameter(0.7, 1.2, default=1.02515, space="buy", optimize=is_optimize_clucha)
    buy_clucha_closedelta_close = DecimalParameter(0.001, 0.05, default=0.04401, space="buy", optimize=is_optimize_clucha)
    buy_clucha_rocr_1h = DecimalParameter(0.1, 1.0, default=0.47782, space="buy", optimize=is_optimize_clucha)

    is_optimize_gumbo = True
    buy_gumbo_ema = DecimalParameter(0.9, 1.2, default=0.97, space="buy", optimize=is_optimize_gumbo)
    buy_gumbo_ewo_low = DecimalParameter(-12.0, 5, default=-5.585, space="buy", optimize=is_optimize_gumbo)
    buy_gumbo_cti = DecimalParameter(-0.9, -0.0, default=-0.5, space="buy", optimize=is_optimize_gumbo)
    buy_gumbo_r14 = DecimalParameter(-100, -44, default=-60, space="buy", optimize=is_optimize_gumbo)

    is_optimize_32 = True
    buy_rsi_fast_32 = IntParameter(20, 70, default=46, space="buy", optimize=is_optimize_32)
    buy_rsi_32 = IntParameter(15, 50, default=19, space="buy", optimize=is_optimize_32)
    buy_sma15_32 = DecimalParameter(0.900, 1, default=0.942, decimals=3, space="buy", optimize=is_optimize_32)
    buy_cti_32 = DecimalParameter(-1, 0, default=-0.86, decimals=2, space="buy", optimize=is_optimize_32)

    is_optimize_vwap = True
    tpc = IntParameter(1, 20, default=4, space="buy", optimize=is_optimize_vwap)
    buy_vwap_cti = DecimalParameter(-1, 0, default=-0.86, decimals=2, space="buy", optimize=is_optimize_vwap)
    buy_vwap_rsi = IntParameter(15, 35, default=35, space="buy", optimize=is_optimize_vwap)

    # custom stoploss
    trailing_optimize = True
    pHSL = DecimalParameter(-0.990, -0.040, default=-0.1, decimals=3, space="sell", optimize=False)
    pPF_1 = DecimalParameter(0.008, 0.030, default=0.03, decimals=3, space="sell", optimize=True)
    pSL_1 = DecimalParameter(0.008, 0.030, default=0.03, decimals=3, space="sell", optimize=trailing_optimize)
    pPF_2 = DecimalParameter(0.040, 0.080, default=0.080, decimals=3, space="sell", optimize=True)
    pSL_2 = DecimalParameter(0.040, 0.080, default=0.080, decimals=3, space="sell", optimize=trailing_optimize)

    sell_long_fastx = IntParameter(50, 100, default=75, space="sell", optimize=True)
    sell_short_fastx = IntParameter(50, 100, default=75, space="sell", optimize=True)

    def custom_stoploss(
        self,
        pair: str,
        trade: Trade,
        current_time: datetime,
        current_rate: float,
        current_profit: float,
        **kwargs,
    ) -> float:
        """
        自定义动态止损函数

        实现分阶段的动态止损机制：
        1. 当盈利低于PF_1时，使用硬止损HSL
        2. 当盈利在PF_1和PF_2之间时，线性插值计算止损
        3. 当盈利高于PF_2时，止损随盈利线性增长

        这种机制可以在保护利润的同时，给予交易更多的盈利空间

        Args:
            pair: 交易对
            trade: 交易对象
            current_time: 当前时间
            current_rate: 当前价格
            current_profit: 当前盈利百分比

        Returns:
            float: 止损百分比，1表示立即止损
        """
        # 获取止损参数值
        HSL = self.pHSL.value  # 硬止损水平
        PF_1 = self.pPF_1.value  # 第一个盈利阈值
        SL_1 = self.pSL_1.value  # 第一阶段止损水平
        PF_2 = self.pPF_2.value  # 第二个盈利阈值
        SL_2 = self.pSL_2.value  # 第二阶段止损水平

        # 分阶段计算止损水平：
        # - 盈利在PF_1和PF_2之间时，止损水平线性插值
        # - 盈利超过PF_2时，止损水平随盈利线性增长
        # - 盈利低于PF_1时，使用硬止损HSL

        if current_profit > PF_2:
            # 高盈利阶段：止损随盈利增长
            sl_profit = SL_2 + (current_profit - PF_2)
        elif current_profit > PF_1:
            # 中等盈利阶段：线性插值计算止损
            sl_profit = SL_1 + ((current_profit - PF_1) * (SL_2 - SL_1) / (PF_2 - PF_1))
        else:
            # 低盈利或亏损阶段：使用硬止损
            sl_profit = HSL

        # 检查止损计算是否有效，避免数学错误
        if self.can_short:
            if (-1 + ((1 - sl_profit) / (1 - current_profit))) <= 0:
                return 1
        else:
            if (1 - ((1 + sl_profit) / (1 + current_profit))) <= 0:
                return 1

        # 返回基于开仓价格的止损水平
        return stoploss_from_open(sl_profit, current_profit, is_short=trade.is_short)

    @informative("1h")
    def populate_indicators_1h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        dataframe["ema_200"] = ta.EMA(dataframe, timeperiod=200)

        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe["ha_close"] = heikinashi["close"]
        dataframe["rocr"] = ta.ROCR(dataframe["ha_close"], timeperiod=28)

        dataframe["roc"] = ta.ROC(dataframe, timeperiod=9)

        # # Bollinger bands
        bollinger2 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=2)
        dataframe["bb_lowerband"] = bollinger2["lower"]
        dataframe["bb_middleband"] = bollinger2["mid"]
        dataframe["bb_upperband"] = bollinger2["upper"]
        dataframe["bb_width"] = (dataframe["bb_upperband"] - dataframe["bb_lowerband"]) / dataframe["bb_middleband"]

        dataframe["T3"] = T3(dataframe)

        return dataframe

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        # Bollinger bands
        bollinger2 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=2)
        dataframe["bb_lowerband"] = bollinger2["lower"]
        dataframe["bb_middleband"] = bollinger2["mid"]
        dataframe["bb_upperband"] = bollinger2["upper"]

        bollinger3 = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=20, stds=3)
        dataframe["bb_lowerband3"] = bollinger3["lower"]
        dataframe["bb_middleband3"] = bollinger3["mid"]
        dataframe["bb_upperband3"] = bollinger3["upper"]

        # Other BB checks
        dataframe["bb_width"] = (dataframe["bb_upperband"] - dataframe["bb_lowerband"]) / dataframe["bb_middleband"]
        dataframe["bb_delta"] = (dataframe["bb_lowerband"] - dataframe["bb_lowerband3"]) / dataframe["bb_lowerband"]

        # BinH
        dataframe["closedelta"] = (dataframe["close"] - dataframe["close"].shift()).abs()

        # SMA
        dataframe["sma_15"] = ta.SMA(dataframe, timeperiod=15)
        dataframe["sma_28"] = ta.SMA(dataframe, timeperiod=28)

        # CTI
        dataframe["cti"] = pta.cti(dataframe["close"], length=20)

        # CRSI (3, 2, 100)
        crsi_closechange = dataframe["close"] / dataframe["close"].shift(1)
        crsi_updown = np.where(crsi_closechange.gt(1), 1.0, np.where(crsi_closechange.lt(1), -1.0, 0.0))
        dataframe["crsi"] = (ta.RSI(dataframe["close"], timeperiod=3) + ta.RSI(crsi_updown, timeperiod=2) + ta.ROC(dataframe["close"], 100)) / 3

        # EMA
        dataframe["ema_8"] = ta.EMA(dataframe, timeperiod=8)
        dataframe["ema_12"] = ta.EMA(dataframe, timeperiod=12)
        dataframe["ema_13"] = ta.EMA(dataframe, timeperiod=13)
        dataframe["ema_16"] = ta.EMA(dataframe, timeperiod=16)
        dataframe["ema_20"] = ta.EMA(dataframe, timeperiod=20)
        dataframe["ema_26"] = ta.EMA(dataframe, timeperiod=26)
        dataframe["ema_100"] = ta.EMA(dataframe, timeperiod=100)
        dataframe["ema_200"] = ta.EMA(dataframe, timeperiod=200)

        # RSI
        dataframe["rsi"] = ta.RSI(dataframe, timeperiod=14)
        dataframe["rsi_fast"] = ta.RSI(dataframe, timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe, timeperiod=20)

        # Elliot
        dataframe["EWO"] = ewo(dataframe, 50, 200)

        # Heiken Ashi
        heikinashi = qtpylib.heikinashi(dataframe)
        dataframe["ha_open"] = heikinashi["open"]
        dataframe["ha_close"] = heikinashi["close"]
        dataframe["ha_high"] = heikinashi["high"]
        dataframe["ha_low"] = heikinashi["low"]

        # BB 40
        bollinger2_40 = qtpylib.bollinger_bands(ha_typical_price(dataframe), window=40, stds=2)
        dataframe["bb_lowerband_40"] = bollinger2_40["lower"]
        dataframe["bb_middleband_40"] = bollinger2_40["mid"]
        dataframe["bb_upperband_40"] = bollinger2_40["upper"]

        # ClucHA
        dataframe["bb_delta_cluc"] = (dataframe["bb_middleband_40"] - dataframe["bb_lowerband_40"]).abs()
        dataframe["ha_closedelta"] = (dataframe["ha_close"] - dataframe["ha_close"].shift()).abs()
        dataframe["tail"] = (dataframe["ha_close"] - dataframe["ha_low"]).abs()
        dataframe["ema_slow"] = ta.EMA(dataframe["ha_close"], timeperiod=50)
        dataframe["rocr"] = ta.ROCR(dataframe["ha_close"], timeperiod=28)

        # vmap indicators
        vwap_low, vwap, vwap_high = vwap_b(dataframe, 20, 1)
        dataframe["vwap_low"] = vwap_low

        for val in self.tpc.range:
            dataframe[f"tcp_percent_{val}"] = top_percent_change(dataframe, val)

        for val in self.buy_cci_length.range:
            dataframe[f"cci_length_{val}"] = ta.CCI(dataframe, val)

        for val in self.buy_rmi_length.range:
            dataframe[f"rmi_length_{val}"] = rmi(dataframe, length=val, mom=4)

        stoch = ta.STOCHRSI(dataframe, 15, 20, 2, 2)
        dataframe["srsi_fk"] = stoch["fastk"]

        # True range
        dataframe["trange"] = ta.TRANGE(dataframe)

        # KC
        dataframe["range_ma_28"] = ta.SMA(dataframe["trange"], 28)
        dataframe["kc_upperband_28_1"] = dataframe["sma_28"] + dataframe["range_ma_28"]
        dataframe["kc_lowerband_28_1"] = dataframe["sma_28"] - dataframe["range_ma_28"]

        # Linreg
        dataframe["hh_20"] = ta.MAX(dataframe["high"], 20)
        dataframe["ll_20"] = ta.MIN(dataframe["low"], 20)
        dataframe["avg_hh_ll_20"] = (dataframe["hh_20"] + dataframe["ll_20"]) / 2
        dataframe["avg_close_20"] = ta.SMA(dataframe["close"], 20)
        dataframe["avg_val_20"] = (dataframe["avg_hh_ll_20"] + dataframe["avg_close_20"]) / 2
        dataframe["linreg_val_20"] = ta.LINEARREG(dataframe["close"] - dataframe["avg_val_20"], 20, 0)

        dataframe["volume_mean_12"] = dataframe["volume"].rolling(12).mean().shift(1)
        dataframe["volume_mean_24"] = dataframe["volume"].rolling(24).mean().shift(1)

        dataframe["r_14"] = williams_r(dataframe, period=14)

        # Cofi
        stoch_fast = ta.STOCHF(dataframe, 5, 3, 0, 3, 0)
        dataframe["fastd"] = stoch_fast["fastd"]
        dataframe["fastk"] = stoch_fast["fastk"]
        dataframe["adx"] = ta.ADX(dataframe)

        # T3 Averag
        dataframe["T3"] = T3(dataframe)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        填充入场趋势信号

        该函数实现了多种买入策略的组合，每种策略都有独立的条件检查：
        1. 布林带检查策略 (bb_checked)
        2. 挤压动量策略 (sqzmom)
        3. EWO策略2 (ewo_2)
        4. R死鱼策略 (r_deadfish)
        5. ClucHA策略 (clucha)
        6. Cofi策略 (cofi)
        7. Gumbo策略 (gumbo)
        8. 本地上升趋势策略 (local_uptrend)
        9. 本地下跌策略 (local_dip)
        10. EWO策略 (ewo)
        11. NFI32策略 (nfi_32)
        12. VWAP策略 (vwap)

        Args:
            dataframe: 包含技术指标的价格数据框
            metadata: 交易对元数据

        Returns:
            DataFrame: 添加了入场信号的数据框
        """
        conditions = []
        dataframe.loc[:, "enter_tag"] = ""

        # 布林带检查策略 - 基于布林带突破和多个超卖指标的组合
        is_bb_checked = (
            self.buy_is_bb_checked_enable.value  # 策略开关
            & (dataframe[f"rmi_length_{self.buy_rmi_length.value}"] < self.buy_rmi.value)  # RMI超卖
            & (dataframe[f"cci_length_{self.buy_cci_length.value}"] <= self.buy_cci.value)  # CCI超卖
            & (dataframe["srsi_fk"] < self.buy_srsi_fk.value)  # 随机RSI超卖
            & (dataframe["bb_delta"] > self.buy_bb_delta.value)  # 布林带差值足够大
            & (dataframe["bb_width"] > self.buy_bb_width.value)  # 布林带宽度足够大
            & (dataframe["closedelta"] > dataframe["close"] * self.break_closedelta.value / 1000)  # 价格变化幅度足够大（来自BinH策略）
            & (dataframe["close"] < dataframe["bb_lowerband3"] * self.break_buy_bb_factor.value)  # 价格低于3倍标准差下轨
        )

        # 挤压动量策略 - 基于布林带和肯特纳通道的挤压状态
        is_sqzmom = (
            self.buy_is_sqzmom_enable.value  # 策略开关
            & (dataframe["bb_lowerband"] < dataframe["kc_lowerband_28_1"])  # 布林带被肯特纳通道挤压
            & (dataframe["bb_upperband"] > dataframe["kc_upperband_28_1"])  # 布林带被肯特纳通道挤压
            & (dataframe["linreg_val_20"].shift(2) > dataframe["linreg_val_20"].shift(1))  # 线性回归值转向
            & (dataframe["linreg_val_20"].shift(1) < dataframe["linreg_val_20"])  # 线性回归值上升
            & (dataframe["linreg_val_20"] < 0)  # 线性回归值为负
            & (dataframe["close"] < dataframe["ema_13"] * self.buy_sqzmom_ema.value)  # 价格低于EMA13
            & (dataframe["EWO"] < self.buy_sqzmom_ewo.value)  # EWO指标条件
            & (dataframe["r_14"] < self.buy_sqzmom_r14.value)  # 威廉指标超卖
        )

        # EWO策略2 - 基于Elliott Wave Oscillator的趋势跟随策略
        is_ewo_2 = (
            self.buy_is_ewo_2_enable.value  # 策略开关
            & (dataframe["ema_200_1h"] > dataframe["ema_200_1h"].shift(12))  # 1小时EMA200上升趋势
            & (dataframe["ema_200_1h"].shift(12) > dataframe["ema_200_1h"].shift(24))  # 持续上升趋势
            & (dataframe["rsi_fast"] < self.buy_rsi_fast_ewo_2.value)  # 快速RSI超卖
            & (dataframe["close"] < dataframe["ema_8"] * self.buy_ema_low_2.value)  # 价格低于EMA8
            & (dataframe["EWO"] > self.buy_ewo_high_2.value)  # EWO指标强势
            & (dataframe["close"] < dataframe["ema_16"] * self.buy_ema_high_2.value)  # 价格低于EMA16
            & (dataframe["rsi"] < self.buy_rsi_ewo_2.value)  # RSI超卖
        )

        # R死鱼策略 - 基于趋势反转和成交量分析的策略
        is_r_deadfish = (
            self.buy_is_r_deadfish_enable.value  # 策略开关
            & (dataframe["ema_100"] < dataframe["ema_200"] * self.buy_r_deadfish_ema.value)  # EMA100低于EMA200，下降趋势
            & (dataframe["bb_width"] > self.buy_r_deadfish_bb_width.value)  # 布林带宽度足够大，有波动性
            & (dataframe["close"] < dataframe["bb_middleband"] * self.buy_r_deadfish_bb_factor.value)  # 价格低于布林带中轨
            & (dataframe["volume_mean_12"] > dataframe["volume_mean_24"] * self.buy_r_deadfish_volume_factor.value)  # 短期成交量高于长期成交量
            & (dataframe["cti"] < self.buy_r_deadfish_cti.value)  # CTI指标超卖
            & (dataframe["r_14"] < self.buy_r_deadfish_r14.value)  # 威廉指标超卖
        )

        # ClucHA策略 - 基于Heiken Ashi蜡烛图的策略
        is_clucha = (
            self.buy_is_clucHA_enable.value  # 策略开关
            & (dataframe["rocr_1h"] > self.buy_clucha_rocr_1h.value)  # 1小时变化率条件
            & (
                (dataframe["bb_lowerband_40"].shift() > 0)  # 布林带下轨有效
                & (dataframe["bb_delta_cluc"] > dataframe["ha_close"] * self.buy_clucha_bbdelta_close.value)  # 布林带差值相对HA收盘价的比例
                & (dataframe["ha_closedelta"] > dataframe["ha_close"] * self.buy_clucha_closedelta_close.value)  # HA收盘价变化相对收盘价的比例
                & (dataframe["tail"] < dataframe["bb_delta_cluc"] * self.buy_clucha_bbdelta_tail.value)  # 下影线长度相对布林带差值的比例
                & (dataframe["ha_close"] < dataframe["bb_lowerband_40"].shift())  # HA收盘价低于布林带下轨
                & (dataframe["ha_close"] < dataframe["ha_close"].shift())  # HA收盘价下降
            )
        )

        is_cofi = (
            self.buy_is_cofi_enable.value
            & (dataframe["roc_1h"] < self.buy_roc_1h.value)
            & (dataframe["bb_width_1h"] < self.buy_bb_width_1h.value)
            & (dataframe["open"] < dataframe["ema_8"] * self.buy_ema_cofi.value)
            & (qtpylib.crossed_above(dataframe["fastk"], dataframe["fastd"]))
            & (dataframe["fastk"] < self.buy_fastk.value)
            & (dataframe["fastd"] < self.buy_fastd.value)
            & (dataframe["adx"] > self.buy_adx.value)
            & (dataframe["EWO"] > self.buy_ewo_high.value)
            & (dataframe["cti"] < self.buy_cofi_cti.value)
            & (dataframe["r_14"] < self.buy_cofi_r14.value)
        )

        is_gumbo = (
            self.buy_is_gumbo_enable.value
            & (dataframe["EWO"] < self.buy_gumbo_ewo_low.value)
            & (dataframe["bb_middleband_1h"] >= dataframe["T3_1h"])
            & (dataframe["T3"] <= dataframe["ema_8"] * self.buy_gumbo_ema.value)
            & (dataframe["cti"] < self.buy_gumbo_cti.value)
            & (dataframe["r_14"] < self.buy_gumbo_r14.value)
        )

        is_local_uptrend = (
            self.buy_is_local_uptrend_enable.value
            & (dataframe["ema_26"] > dataframe["ema_12"])
            & (dataframe["ema_26"] - dataframe["ema_12"] > dataframe["open"] * self.buy_ema_diff.value)
            & (dataframe["ema_26"].shift() - dataframe["ema_12"].shift() > dataframe["open"] / 100)
            & (dataframe["close"] < dataframe["bb_lowerband"] * self.buy_bb_factor.value)
            & (dataframe["closedelta"] > dataframe["close"] * self.buy_closedelta.value / 1000)
        )

        is_local_dip = (
            self.buy_is_local_dip_enable.value
            & (dataframe["ema_26"] > dataframe["ema_12"])
            & (dataframe["ema_26"] - dataframe["ema_12"] > dataframe["open"] * self.buy_ema_diff_local_dip.value)
            & (dataframe["ema_26"].shift() - dataframe["ema_12"].shift() > dataframe["open"] / 100)
            & (dataframe["close"] < dataframe["ema_20"] * self.buy_ema_high_local_dip.value)
            & (dataframe["rsi"] < self.buy_rsi_local_dip.value)
            & (dataframe["crsi"] > self.buy_crsi_local_dip.value)
            & (dataframe["closedelta"] > dataframe["close"] * self.buy_closedelta_local_dip.value / 1000)
        )

        is_ewo = (  # from SMA offset
            self.buy_is_ewo_enable.value
            & (dataframe["rsi_fast"] < self.buy_rsi_fast.value)
            & (dataframe["close"] < dataframe["ema_8"] * self.buy_ema_low.value)
            & (dataframe["EWO"] > self.buy_ewo.value)
            & (dataframe["close"] < dataframe["ema_16"] * self.buy_ema_high.value)
            & (dataframe["rsi"] < self.buy_rsi.value)
        )

        is_nfi_32 = (
            self.buy_is_nfi_32_enable.value
            & (dataframe["rsi_slow"] < dataframe["rsi_slow"].shift(1))
            & (dataframe["rsi_fast"] < self.buy_rsi_fast_32.value)
            & (dataframe["rsi"] > self.buy_rsi_32.value)
            & (dataframe["close"] < dataframe["sma_15"] * self.buy_sma15_32.value)
            & (dataframe["cti"] < self.buy_cti_32.value)
        )

        is_vwap = (
            self.buy_is_vwap_enable.value
            & (dataframe["close"] < dataframe["vwap_low"])
            & (dataframe[f"tcp_percent_{self.tpc.value}"] > self.tpc.value)
            & (dataframe["cti"] < self.buy_vwap_cti.value)
            & (dataframe["rsi"] < self.buy_vwap_rsi.value)
        )

        conditions.append(is_bb_checked)
        dataframe.loc[is_bb_checked, "enter_tag"] += "bb "

        conditions.append(is_sqzmom)
        dataframe.loc[is_sqzmom, "enter_tag"] += "sqzmom "

        conditions.append(is_ewo_2)
        dataframe.loc[is_ewo_2, "enter_tag"] += "ewo2 "

        conditions.append(is_r_deadfish)
        dataframe.loc[is_r_deadfish, "enter_tag"] += "r_deadfish "

        conditions.append(is_clucha)
        dataframe.loc[is_clucha, "enter_tag"] += "clucHA "

        conditions.append(is_cofi)
        dataframe.loc[is_cofi, "enter_tag"] += "cofi "

        conditions.append(is_gumbo)
        dataframe.loc[is_gumbo, "enter_tag"] += "gumbo "

        conditions.append(is_local_uptrend)
        dataframe.loc[is_local_uptrend, "enter_tag"] += "local_uptrend "

        conditions.append(is_local_dip)
        dataframe.loc[is_local_dip, "enter_tag"] += "local_dip "

        conditions.append(is_ewo)
        dataframe.loc[is_ewo, "enter_tag"] += "ewo "

        conditions.append(is_nfi_32)
        dataframe.loc[is_nfi_32, "enter_tag"] += "nfi_32 "

        conditions.append(is_vwap)
        dataframe.loc[is_vwap, "enter_tag"] += "vwap "

        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "enter_long"] = 1

        # =============================================================================
        # 做空条件 - 基于买入条件的反向逻辑
        # =============================================================================
        short_conditions = []

        # 布林带检查做空策略 - 基于布林带突破和多个超买指标的组合
        is_bb_checked_short = (
            self.sell_is_bb_checked_enable.value  # 策略开关
            & (dataframe[f"rmi_length_{self.buy_rmi_length.value}"] > (100 - self.buy_rmi.value))  # RMI超买
            & (dataframe[f"cci_length_{self.buy_cci_length.value}"] >= -self.buy_cci.value)  # CCI超买
            & (dataframe["srsi_fk"] > (100 - self.buy_srsi_fk.value))  # 随机RSI超买
            & (dataframe["bb_delta"] > self.buy_bb_delta.value)  # 布林带差值足够大
            & (dataframe["bb_width"] > self.buy_bb_width.value)  # 布林带宽度足够大
            & (dataframe["closedelta"] > dataframe["close"] * self.break_closedelta.value / 1000)  # 价格变化幅度足够大
            & (dataframe["close"] > dataframe["bb_upperband3"] * (2 - self.break_buy_bb_factor.value))  # 价格高于3倍标准差上轨
        )

        # 挤压动量做空策略 - 基于布林带和肯特纳通道的挤压状态
        is_sqzmom_short = (
            self.sell_is_sqzmom_enable.value  # 策略开关
            & (dataframe["bb_lowerband"] < dataframe["kc_lowerband_28_1"])  # 布林带被肯特纳通道挤压
            & (dataframe["bb_upperband"] > dataframe["kc_upperband_28_1"])  # 布林带被肯特纳通道挤压
            & (dataframe["linreg_val_20"].shift(2) < dataframe["linreg_val_20"].shift(1))  # 线性回归值转向
            & (dataframe["linreg_val_20"].shift(1) > dataframe["linreg_val_20"])  # 线性回归值下降
            & (dataframe["linreg_val_20"] > 0)  # 线性回归值为正
            & (dataframe["close"] > dataframe["ema_13"] * (2 - self.buy_sqzmom_ema.value))  # 价格高于EMA13
            & (dataframe["EWO"] > -self.buy_sqzmom_ewo.value)  # EWO指标条件
            & (dataframe["r_14"] > (100 + self.buy_sqzmom_r14.value))  # 威廉指标超买
        )

        # EWO做空策略2 - 基于Elliott Wave Oscillator的趋势跟随策略
        is_ewo_2_short = (
            self.sell_is_ewo_2_enable.value  # 策略开关
            & (dataframe["ema_200_1h"] < dataframe["ema_200_1h"].shift(12))  # 1小时EMA200下降趋势
            & (dataframe["ema_200_1h"].shift(12) < dataframe["ema_200_1h"].shift(24))  # 持续下降趋势
            & (dataframe["rsi_fast"] > (100 - self.buy_rsi_fast_ewo_2.value))  # 快速RSI超买
            & (dataframe["close"] > dataframe["ema_8"] * (2 - self.buy_ema_low_2.value))  # 价格高于EMA8
            & (dataframe["EWO"] < -self.buy_ewo_high_2.value)  # EWO指标弱势
            & (dataframe["close"] > dataframe["ema_16"] * (2 - self.buy_ema_high_2.value))  # 价格高于EMA16
            & (dataframe["rsi"] > (100 - self.buy_rsi_ewo_2.value))  # RSI超买
        )

        short_conditions.append(is_bb_checked_short)
        dataframe.loc[is_bb_checked_short, "enter_tag"] += "bb_short "

        short_conditions.append(is_sqzmom_short)
        dataframe.loc[is_sqzmom_short, "enter_tag"] += "sqzmom_short "

        short_conditions.append(is_ewo_2_short)
        dataframe.loc[is_ewo_2_short, "enter_tag"] += "ewo2_short "

        if short_conditions:
            dataframe.loc[reduce(lambda x, y: x | y, short_conditions), "enter_short"] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        conditions = []
        short_conditions = []
        dataframe.loc[:, "exit_tag"] = ""

        # 多头出场条件
        fastk_cross = qtpylib.crossed_above(dataframe["fastk"], self.sell_long_fastx.value)
        conditions.append(fastk_cross)
        dataframe.loc[fastk_cross, "exit_tag"] += "fastk_cross "

        if conditions:
            dataframe.loc[reduce(lambda x, y: x | y, conditions), "exit_long"] = 1

        # 空头出场条件 - 与多头出场条件相反
        fastk_cross_short = qtpylib.crossed_below(dataframe["fastk"], 100 - self.sell_short_fastx.value)
        short_conditions.append(fastk_cross_short)
        dataframe.loc[fastk_cross_short, "exit_tag"] += "fastk_cross_short "

        if short_conditions:
            dataframe.loc[reduce(lambda x, y: x | y, short_conditions), "exit_short"] = 1

        return dataframe

    def leverage(
        self,
        pair: str,
        current_time: datetime,
        current_rate: float,
        proposed_leverage: float,
        max_leverage: float,
        entry_tag: str,
        side: str,
        **kwargs,
    ) -> float:
        """
        杠杆倍数设置 - 从buy_leverage参数读取杠杆配置

        从buy_leverage参数读取杠杆设置，如果buy_leverage参数没有设置则使用默认值1.0

        Args:
            pair: 交易对
            current_time: 当前时间
            current_rate: 当前价格
            proposed_leverage: 建议杠杆倍数
            max_leverage: 最大杠杆倍数
            entry_tag: 入场标签
            side: 交易方向

        Returns:
            float: 实际使用的杠杆倍数
        """

        return min(max(float(self.buy_leverage.value), 1.0), max_leverage)
