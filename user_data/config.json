{
  "trading_mode": "futures",
  "margin_mode": "isolated",
  "stake_currency": "USDT",
  "leverage": 4.0,
  "datadir": "../data/binance",
  "stake_amount": "unlimited",
  "dry_run_wallet": 5000,
  "tradable_balance_ratio": 0.9,
  "fiat_display_currency": "CNY",
  "dry_run": true,
  "force_entry_enable": true,
  "initial_state": "running",
  "internals": {
    "process_throttle_secs": 5
  },
  "cancel_open_orders_on_exit": false,
  "unfilledtimeout": {
    "entry": 10,
    "exit": 30
  },
  "entry_pricing": {
    "price_side": "other",
    "use_order_book": true,
    "order_book_top": 1,
    "price_last_balance": 0.0,
    "check_depth_of_market": {
      "enabled": false,
      "bids_to_ask_delta": 1
    }
  },
  "exit_pricing": {
    "price_side": "other",
    "use_order_book": true,
    "order_book_top": 1
  },
  "exchange": {
    "name": "binance",
    "sandbox": false,
    "key": "",
    "secret": "",
    "enable_ws": true,
    "ccxt_config": {
      // "enableRateLimit": false,
      "httpsProxy": "http://localhost:3213",
      "wsProxy": "http://localhost:3213"
    },
    "ccxt_async_config": {
      // "enableRateLimit": false
    },
    "pair_whitelist": [
      "BTC/USDT:USDT",
      "ETH/USDT:USDT",
      "XRP/USDT:USDT",
      "SOL/USDT:USDT",
      "TRX/USDT:USDT",
      "DOGE/USDT:USDT",
      "ADA/USDT:USDT",
      "SUI/USDT:USDT",
      "BCH/USDT:USDT",
      "LINK/USDT:USDT",
      "AVAX/USDT:USDT"
      // "XLM/USDT:USDT",
      // "TON/USDT:USDT",
      // "ORDI/USDT:USDT",
      // "LTC/USDT:USDT",
      // "HBAR/USDT:USDT",
      // "XMR/USDT:USDT",
      // "DOT/USDT:USDT",
      // "UNI/USDT:USDT",
      // "AAVE/USDT:USDT",
      // "1000PEPE/USDT:USDT",
      // "TAO/USDT:USDT",
      // "APT/USDT:USDT",
      // "NEAR/USDT:USDT",
      // "ICP/USDT:USDT",
      // "LDO/USDT:USDT",
      // "ETC/USDT:USDT",
      // "ONDO/USDT:USDT",
      // "KAS/USDT:USDT",
      // "POL/USDT:USDT",
      // "PENDLE/USDT:USDT",
      // "VET/USDT:USDT",
      // "1000BONK/USDT:USDT",
      // "RENDER/USDT:USDT",
      // "ENA/USDT:USDT",
      // "ARB/USDT:USDT",
      // "ATOM/USDT:USDT",
      // "FET/USDT:USDT",
      // "FIL/USDT:USDT",
      // "ALGO/USDT:USDT",
      // "WLD/USDT:USDT",
      // "SEI/USDT:USDT",
      // "THETA/USDT:USDT",
      // "IMX/USDT:USDT",
      // "OP/USDT:USDT",
      // "JASMY/USDT:USDT",
      // "STX/USDT:USDT",
      // "INJ/USDT:USDT",
      // "SAND/USDT:USDT",
      // "MANA/USDT:USDT"
    ],
    "pair_blacklist": []
  },
  "pairlists": [
    {
      "method": "StaticPairList"
    },
    {
      "method": "ShuffleFilter",
      "shuffle_frequency": "candle",
      "seed": 42
    }
  ],
  "api_server": {
    "enabled": true,
    "listen_ip_address": "0.0.0.0",
    "listen_port": 8080,
    "verbosity": "error",
    "enable_openapi": false,
    "jwt_secret_key": "a741e55c8f8a4f83b754875586fee12d9478f342c19743ab8724c127bb5346ef",
    "ws_token": "",
    "CORS_origins": ["http://frequi"],
    "username": "cz",
    "password": "juedui"
  }
}
