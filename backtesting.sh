#!/usr/bin/env bash
# shellcheck disable=SC1091

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

选项:
    -h, --help           显示此帮助信息
    -d, --data-dir DIR   数据目录 (覆盖 DATA_DIR)
    -c, --config FILE    配置文件 (覆盖 CONFIG_FILE)
    -s, --strategy NAME  策略名称 (覆盖 STRATEGY)
    --clean              清理之前的回测结果
    --start-date DATE    开始日期 (覆盖 START_DATE)
    --end-date DATE      结束日期 (覆盖 END_DATE)
    --strategy-path PATH 策略路径 (覆盖 STRATEGY_PATH)
    --freqai-model MODEL 使用FreqAI模型

示例:
    $0 -s MyStrategy --start-date 20240101
    $0 -d /path/to/data -c config/test.json
    $0 --strategy-path custom/strategies -s TestStrategy --clean

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 默认设置
CLEAN_RESULTS=false
FREQAI_MODEL=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    -d | --data-dir)
        DATA_DIR="$2"
        shift 2
        ;;
    -c | --config)
        CONFIG_FILE="$2"
        shift 2
        ;;
    -s | --strategy)
        STRATEGY="$2"
        shift 2
        ;;
    --start-date)
        START_DATE="$2"
        shift 2
        ;;
    --end-date)
        END_DATE="$2"
        shift 2
        ;;
    --strategy-path)
        STRATEGY_PATH="$2"
        shift 2
        ;;
    --clean)
        CLEAN_RESULTS=true
        shift
        ;;
    --freqai-model)
        FREQAI_MODEL="$2"
        shift 2
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

# 显示当前配置
echo "=== 回测配置 ==="
echo "数据目录: ${DATA_DIR}"
echo "配置文件: ${CONFIG_FILE}"
echo "策略名称: ${STRATEGY}"
echo "开始日期: ${START_DATE}"
echo "结束日期: ${END_DATE}"
echo "策略路径: ${STRATEGY_PATH}"
if [[ -n "$FREQAI_MODEL" ]]; then
    echo "FreqAI模型: ${FREQAI_MODEL}"
fi
echo "================"

# 清理之前的回测结果（如果启用）
if [[ "$CLEAN_RESULTS" == "true" ]]; then
    echo "清理之前的回测结果..."
    rm -rf user_data/backtest_results
fi

# 构建freqtrade命令
if [[ -z "$END_DATE" ]]; then
    END_DATE=$(date +"%Y%m%d")
fi
freqtrade_cmd=(
    freqtrade backtesting
    --data-dir "${DATA_DIR}"
    --config "${CONFIG_FILE}"
    --timerange="${START_DATE}-${END_DATE}"
    --strategy-path "${STRATEGY_PATH}"
    --strategy "${STRATEGY}"
    --breakdown day week month
    --enable-protections
)

# 如果指定了FreqAI模型，添加到命令中
if [[ -n "$FREQAI_MODEL" ]]; then
    freqtrade_cmd+=(--freqaimodel "${FREQAI_MODEL}")
fi

# 执行回测
"${freqtrade_cmd[@]}"
